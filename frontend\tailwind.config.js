/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class', // Enable class-based dark mode
  theme: {
    extend: {
      colors: {
        primary: {
          light: '#6366f1', // Indigo-500
          DEFAULT: '#4f46e5', // Indigo-600
          dark: '#4338ca', // Indigo-700
        },
        secondary: {
          light: '#a855f7', // Purple-500
          DEFAULT: '#9333ea', // Purple-600
          dark: '#7e22ce', // Purple-700
        },
        background: {
          light: '#ffffff',
          dark: '#1f2937', // Gray-800
        },
        surface: {
          light: '#f9fafb', // Gray-50
          dark: '#111827', // Gray-900
        },
        text: {
          light: '#111827', // Gray-900
          dark: '#f9fafb', // Gray-50
        },
        gray: {
          850: '#1a202e', // Custom dark gray between gray-800 and gray-900
        },
      },
      fontFamily: {
        sans: ['Inter', 'ui-sans-serif', 'system-ui', 'sans-serif'],
      },
      fontSize: {
        'xs': ['0.875rem', { lineHeight: '1.5' }], // 14px
        'sm': ['1rem', { lineHeight: '1.5' }], // 16px
        'base': ['1.125rem', { lineHeight: '1.6' }], // 18px
        'lg': ['1.25rem', { lineHeight: '1.6' }], // 20px
        'xl': ['1.375rem', { lineHeight: '1.6' }], // 22px
        '2xl': ['1.5rem', { lineHeight: '1.6' }], // 24px
        '3xl': ['1.875rem', { lineHeight: '1.6' }], // 30px
      },
      typography: {
        DEFAULT: {
          css: {
            fontSize: '1.125rem', // 18px base
            lineHeight: '1.6',
            p: {
              fontSize: '1.125rem',
              lineHeight: '1.6',
            },
            li: {
              fontSize: '1.125rem',
              lineHeight: '1.6',
            },
            code: {
              fontSize: '1rem',
            },
            pre: {
              fontSize: '0.875rem',
            },
          },
        },
        lg: {
          css: {
            fontSize: '1.25rem', // 20px
            lineHeight: '1.7',
            p: {
              fontSize: '1.25rem',
              lineHeight: '1.7',
            },
            li: {
              fontSize: '1.25rem',
              lineHeight: '1.7',
            },
          },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
}
