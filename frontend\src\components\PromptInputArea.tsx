import React, { useState, useCallback } from 'react';
import { useConfigStore } from '../store/configStore';
import { getSocket } from '../services/socketService';
import ImageSizeSelector from './ImageSizeSelector';
import ImageStyleSelector from './ImageStyleSelector';
// import axios from 'axios'; // Removed unused import

// Define props interface
interface PromptInputAreaProps {
  toggleConsole: () => void;
  clearChat: () => void;
  currentConversationId: string | null; // Add prop for current conversation ID
  // Add state setters for immediate UI update
  setAgentDiscussion: React.Dispatch<React.SetStateAction<Record<string, string>>>;
  setDiscussionOrder: React.Dispatch<React.SetStateAction<string[]>>;
}

const PromptInputArea: React.FC<PromptInputAreaProps> = ({ toggleConsole, clearChat, currentConversationId, setAgentDiscussion, setDiscussionOrder }) => {
  const [prompt, setPrompt] = useState('');
  const [isDragging, setIsDragging] = useState(false);
  // uploadStatus state is removed, will be managed in RagSettingsTab
  // fileInputRef is removed, will be managed in RagSettingsTab
  const [selectedImage, setSelectedImage] = useState<File | null>(null); // State for selected image file
  const [imagePreviewUrl, setImagePreviewUrl] = useState<string | null>(null); // State for image preview URL
  const [isImageGenerationMode, setIsImageGenerationMode] = useState(false); // State to track image generation mode

  // Image generation options
  const [imageSize, setImageSize] = useState<string>('1024x1024');
  const [imageQuality, setImageQuality] = useState<string>('standard');
  const [imageStyle, setImageStyle] = useState<string>('vivid');
  const [showImageOptions, setShowImageOptions] = useState<boolean>(false);

  // Get relevant config state from Zustand
  const config = useConfigStore((state) => ({
    agentCount: state.agentCount,
    generalInstructions: state.generalInstructions,
    agentConfigurations: state.agentConfigurations,
    internetSettings: state.internetSettings,
    searchAgentSettings: state.searchAgentSettings, // Include searchAgentSettings
    ragSettings: state.ragSettings,
    maxAgentRuns: state.maxAgentRuns,
    baseInstructions: state.baseInstructions,
    useBaseInstructions: state.useBaseInstructions, // Include useBaseInstructions
  }));
  // Get processing state and setters from store
  const isProcessing = useConfigStore((state) => state.isProcessing);
  const setIsProcessing = useConfigStore((state) => state.setIsProcessing);
  const isCancelling = useConfigStore((state) => state.isCancelling); // <<< Get cancelling state
  const setIsCancelling = useConfigStore((state) => state.setIsCancelling); // <<< Get cancelling setter
  // Get error setter for validation feedback
  const setGlobalError = useConfigStore((state) => state.setGlobalError);
  const clearGlobalMessages = useConfigStore((state) => state.clearGlobalMessages);


  // Function to handle sending the prompt
  const sendPrompt = useCallback(async (forceImageGeneration = false) => { // Make async for potential image processing
    clearGlobalMessages();
    // Allow sending if there's a prompt OR an image selected
    if ((!prompt.trim() && !selectedImage) || isProcessing) return;

    // --- Validation ---
    for (let i = 0; i < config.agentConfigurations.length; i++) {
        const agentConfig = config.agentConfigurations[i];
        if (!agentConfig.provider || !agentConfig.model) {
            const errorMsg = `Agent ${i + 1} is missing a Provider or Model selection. Please configure all active agents before sending a prompt.`;
            console.error(errorMsg);
            setGlobalError(errorMsg); // Show error using global feedback
            // alert(errorMsg); // Alternative: use alert
            return; // Stop execution
        }
    }
    // --- End Validation ---

    // Check if we're in image generation mode or it was forced
    const isImageRequest = isImageGenerationMode || forceImageGeneration;
    console.log(`Sending prompt: ${prompt}, Image: ${selectedImage?.name}, Image Generation Mode: ${isImageRequest}`);
    console.log('Current config:', config);

    // Add image generation parameters to the prompt if in image mode
    let finalPrompt = prompt;
    if (isImageRequest) {
      // Only add size to the prompt if it's not already included
      if (!finalPrompt.includes(imageSize)) {
        finalPrompt = `${finalPrompt} ${imageSize}`;
      }

      // Add quality indicator if HD is selected and not already in prompt
      if (imageQuality === 'hd' && !finalPrompt.toLowerCase().includes('hd') &&
          !finalPrompt.toLowerCase().includes('high quality')) {
        finalPrompt = `${finalPrompt} high quality`;
      }

      // Add style indicator if natural is selected and not already in prompt
      if (imageStyle === 'natural' && !finalPrompt.toLowerCase().includes('natural') &&
          !finalPrompt.toLowerCase().includes('realistic')) {
        finalPrompt = `${finalPrompt} natural style`;
      }
    }

    let imageDataUrl: string | null = null;
    if (selectedImage) {
        console.log("Processing image file:", selectedImage.name, "Size:", selectedImage.size, "Type:", selectedImage.type);
        // Convert image to base64 data URL to send via socket
        try {
            imageDataUrl = await new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onloadend = () => {
                    console.log("Image file read successfully, data URL length:", (reader.result as string).length);
                    resolve(reader.result as string);
                };
                reader.onerror = reject;
                reader.readAsDataURL(selectedImage);
            });
        } catch (error) {
             console.error("Error reading image file:", error);
             setGlobalError("Failed to process image file.");
             return; // Don't proceed if image reading fails
        }
    }

    const socket = getSocket();
    if (socket?.connected) {
      console.log("Socket connected, sending prompt with image data:", {
        promptLength: finalPrompt.length,
        hasImageData: !!imageDataUrl,
        imageDataLength: imageDataUrl ? imageDataUrl.length : 0,
        isImageRequest: isImageRequest,
        conversationId: currentConversationId
      });
      setIsProcessing(true);
      // Include conversationId, image data, and isImageRequest flag in the payload
      socket.emit('send_prompt', {
          prompt: finalPrompt, // Use the modified prompt with image parameters
          config,
          conversationId: currentConversationId,
          imageDataUrl: imageDataUrl, // Add image data if available
          isImageRequest: isImageRequest // Add explicit flag for image generation
      });
      console.log("Prompt sent to server, waiting for response...");
      // --- Immediately add user message to UI state ---
      const userMessageKey = `user_${Date.now()}`; // Create a unique key
      setAgentDiscussion(prev => ({
          ...prev,
          [userMessageKey]: finalPrompt // Add user prompt content with image parameters
      }));
      setDiscussionOrder(prev => [...prev, userMessageKey]); // Add key to order
      // --- End immediate UI update ---
      setPrompt(''); // Clear the input field
      setSelectedImage(null); // Clear image after sending
      setImagePreviewUrl(null);
      setIsImageGenerationMode(false); // Reset image generation mode after sending
    } else {
      console.error('Socket not connected. Cannot send prompt.');
      setGlobalError('Error: Cannot connect to server. Please check connection and try again.');
      // alert('Error: Cannot connect to server. Please check connection and try again.');
    }
  }, [prompt, selectedImage, config, isProcessing, currentConversationId, isImageGenerationMode, imageSize, imageQuality, imageStyle, clearGlobalMessages, setGlobalError, setIsProcessing, setAgentDiscussion, setDiscussionOrder]);

  // Function to handle stopping the processing
  const stopProcessing = useCallback(() => {
      if (!isProcessing) return;

      const socket = getSocket();
      if (socket?.connected) {
          console.log('Sending stop_processing event...');
          socket.emit('stop_processing');
          setIsCancelling(true); // <<< Set cancelling state to true
          // Note: We don't immediately set isProcessing to false here.
          // The backend should confirm completion/cancellation via 'discussion_completed'.
      } else {
          console.error('Socket not connected. Cannot send stop signal.');
          setGlobalError('Error: Cannot connect to server to send stop signal.');
          // alert('Error: Cannot connect to server to send stop signal.');
      }
  }, [isProcessing, setGlobalError, setIsCancelling]); // <<< Add setIsCancelling dependency

  // Handle Ctrl+Enter to send prompt
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && e.ctrlKey && !isProcessing) { // Check isProcessing
      e.preventDefault(); // Prevent default Enter behavior (new line)
      sendPrompt(false); // Regular text prompt
    }
  };

  // Toggle image generation mode
  const toggleImageGenerationMode = () => {
    const newMode = !isImageGenerationMode;
    setIsImageGenerationMode(newMode);
    // Show image options when entering image mode
    if (newMode) {
      setShowImageOptions(true);
    }
  };

  // Toggle image options panel
  const toggleImageOptions = () => {
    setShowImageOptions(prev => !prev);
  };

  // Send as image generation request
  const sendImageGenerationRequest = () => {
    sendPrompt(true); // Force image generation
  };

  // --- File Handling Logic (Handles Drag-and-Drop for Images/Docs, Image Selection) ---
  const handleFile = async (file: File) => {
    // Reset image states
    setSelectedImage(null);
    setImagePreviewUrl(null);
    // Reset RAG upload status (now handled in RagSettingsTab)
    // setUploadStatus(null);

    // Check if it's an image for preview/sending with prompt
    if (file.type.startsWith('image/')) {
        console.log('Image selected:', file.name);
        setSelectedImage(file);
        // Generate preview URL
        const reader = new FileReader();
        reader.onloadend = () => {
            setImagePreviewUrl(reader.result as string);
        };
        reader.readAsDataURL(file);
        // setUploadStatus(`Image "${file.name}" ready to send with prompt.`); // Removed
        // setTimeout(() => setUploadStatus(null), 4000); // Removed
    }
    // Check if it's a document for RAG upload (via Drag-and-Drop)
    else if (['.pdf', '.docx', '.txt', '.md'].some(ext => file.name.toLowerCase().endsWith(ext))) {
        console.log('Document dropped for RAG upload:', file.name);
        // Initiate upload but status is handled in RagSettingsTab now
        // We need a way to trigger the upload from here or pass the file up
        // For now, just log it. A better solution might involve a shared upload service/state.
        // Or perhaps drag-drop for RAG should ONLY be on the RAG tab?
        // Let's keep the D&D here for now but remove the direct upload call.
        // uploadFileForRag(file); // Removed direct call
        console.warn("Drag-and-drop RAG upload initiated from PromptInputArea - status not shown here.");
        // TODO: Refactor RAG upload trigger/status feedback for D&D from this area.
    }
    // Handle unsupported file types
    else {
        console.warn('Unsupported file type dropped:', file.type, file.name);
        // setUploadStatus(`Error: Unsupported file type "${file.type}". Please upload images, PDF, DOCX, TXT, or MD.`); // Status handled elsewhere
    }
  };

  // Specific function for uploading files to RAG backend - REMOVED from this component
  // const uploadFileForRag = async (file: File) => { ... };

  // Drag-and-drop handlers
  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };
  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    // Check if leaving to a child element before setting isDragging to false
    if (e.relatedTarget && (e.currentTarget as Node).contains(e.relatedTarget as Node)) {
      return;
    }
    setIsDragging(false);
  };
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault(); // Necessary to allow dropping
    e.stopPropagation();
    setIsDragging(true); // Keep highlighting while dragging over
  };
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    // setUploadStatus(null); // Clear previous status on new drop - Removed

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      // Handle multiple files if needed, for now just upload the first one
      const file = e.dataTransfer.files[0];
      console.log('File dropped:', file);
      handleFile(file); // Use the unified handler
      e.dataTransfer.clearData();
    }
  };

  // Handle file input change (for browse button) - REMOVED from this component
  // const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => { ... };
  // --- End File Upload Logic ---


  return (
    <div
      className={`relative bg-gray-100 dark:bg-gray-850 rounded-xl p-6 border ${isDragging ? 'border-primary dark:border-primary-light border-dashed' : 'border-gray-200 dark:border-gray-700'} shadow-lg w-full max-w-full transition-all duration-200`}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      <div className={`relative ${isImageGenerationMode ? 'border-2 border-purple-500 rounded-xl overflow-hidden' : ''}`}>
        {isImageGenerationMode && (
          <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-purple-500 to-indigo-600 py-2 px-4 text-white text-sm font-medium z-10 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
            </svg>
            Image Generation Mode
          </div>
        )}
        <textarea
          className={`w-full h-40 p-5 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed text-lg shadow-inner max-w-full transition-colors ${isImageGenerationMode ? 'pt-12 border-purple-500' : ''}`}
          placeholder={isImageGenerationMode ? "Describe the image you want to generate..." : "Enter your prompt here, or drag & drop an image... (Ctrl+Enter to send)"}
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          onKeyDown={handleKeyDown}
          disabled={isProcessing}
        ></textarea>
      </div>
      <div className="flex flex-wrap justify-between items-center mt-5 gap-3">
        <div className="flex flex-wrap items-center gap-3">
          <button
            onClick={() => sendPrompt(false)}
            className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-primary to-blue-600 text-white rounded-xl font-semibold text-base shadow-lg hover:shadow-xl hover:from-primary-dark hover:to-blue-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:-translate-y-0.5"
            disabled={(!prompt.trim() && !selectedImage) || isProcessing}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
            </svg>
            Send
          </button>
          <div className="flex flex-col">
            <button
              onClick={isImageGenerationMode ? sendImageGenerationRequest : toggleImageGenerationMode}
              className={`flex items-center gap-2 px-6 py-3 rounded-xl font-semibold text-base shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:-translate-y-0.5 ${
                isImageGenerationMode
                  ? 'bg-gradient-to-r from-purple-600 to-pink-500 text-white hover:from-purple-700 hover:to-pink-600'
                  : 'bg-gradient-to-r from-purple-400 to-blue-400 text-white hover:from-purple-500 hover:to-blue-500'
              }`}
              disabled={(!prompt.trim() && !selectedImage) || isProcessing}
              title={isImageGenerationMode ? "Generate Image" : "Switch to Image Generation Mode"}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
              </svg>
              {isImageGenerationMode ? "Generate Image" : "Image Mode"}
            </button>
            {isImageGenerationMode && (
              <button
                onClick={toggleImageOptions}
                className="text-sm mt-1 text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 self-center"
              >
                {showImageOptions ? "Hide Options" : "Show Options"}
              </button>
            )}
          </div>
          <button
            onClick={stopProcessing}
            className={`flex items-center gap-2 px-6 py-3 rounded-xl font-semibold text-base shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:-translate-y-0.5 ${
              isCancelling
                ? 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white'
                : 'bg-gradient-to-r from-red-500 to-red-700 text-white hover:from-red-600 hover:to-red-800'
            }`}
            disabled={!isProcessing || isCancelling}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clipRule="evenodd" />
            </svg>
            {isCancelling ? 'Stopping...' : 'Stop'}
          </button>
          <button
            onClick={() => {
              setPrompt(''); // Clear local input
              clearChat(); // Call function passed from App to clear discussion/answer state
            }}
            className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-gray-300 to-gray-400 dark:from-gray-700 dark:to-gray-800 text-gray-700 dark:text-gray-200 rounded-xl font-semibold text-base shadow-lg hover:shadow-xl hover:from-gray-400 hover:to-gray-500 dark:hover:from-gray-600 dark:hover:to-gray-900 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:-translate-y-0.5"
            disabled={isProcessing}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            Clear
          </button>
        </div>
        <button
          onClick={toggleConsole}
          className="flex items-center gap-2 px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-xl font-semibold text-base shadow-lg hover:shadow-xl hover:bg-gray-300 dark:hover:bg-gray-600 transition-all duration-200 transform hover:-translate-y-0.5"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
          </svg>
          Toggle Console
        </button>
      </div>
       {isDragging && (
         <div className="absolute inset-0 bg-primary/20 dark:bg-primary-light/20 flex items-center justify-center pointer-events-none rounded-xl border-2 border-dashed border-primary dark:border-primary-light backdrop-blur-sm">
           <div className="bg-white/80 dark:bg-gray-800/80 p-4 rounded-lg shadow-lg">
             <p className="text-primary dark:text-primary-light font-semibold text-lg flex items-center">
               <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" viewBox="0 0 20 20" fill="currentColor">
                 <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
               </svg>
               Drop image here
             </p>
           </div>
         </div>
       )}
       {/* Image Generation Options */}
       {isImageGenerationMode && showImageOptions && (
         <div className="mt-5 p-4 border border-gray-300 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700/50 shadow-md">
           <div className="flex justify-between items-center mb-3">
             <h3 className="text-md font-semibold text-gray-700 dark:text-gray-300">Image Generation Options</h3>
             <button
               onClick={toggleImageOptions}
               className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
               title="Hide options"
             >
               <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                 <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
               </svg>
             </button>
           </div>
           <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
             <ImageSizeSelector onSizeSelect={setImageSize} selectedSize={imageSize} />
             <ImageStyleSelector
               onQualitySelect={setImageQuality}
               onStyleSelect={setImageStyle}
               selectedQuality={imageQuality}
               selectedStyle={imageStyle}
             />
           </div>
           <div className="mt-3 text-sm text-gray-500 dark:text-gray-400">
             <p>Current settings: {imageSize}, {imageQuality} quality, {imageStyle} style</p>
             <p className="mt-1">These settings will be added to your prompt automatically.</p>
           </div>
         </div>
       )}

       {/* Enhanced Image Preview Area */}
       {imagePreviewUrl && (
         <div className="mt-5 p-4 border border-gray-300 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700/50 inline-block relative shadow-md">
           <p className="text-sm text-gray-600 dark:text-gray-400 mb-2 font-medium flex items-center">
             <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
               <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
             </svg>
             Attached Image:
           </p>
           <img
             src={imagePreviewUrl}
             alt="Selected preview"
             className="max-h-32 max-w-xs object-contain rounded-lg shadow-md"
           />
           <button
             onClick={() => { setSelectedImage(null); setImagePreviewUrl(null); }}
             className="absolute -top-2 -right-2 bg-red-600 text-white rounded-full h-7 w-7 flex items-center justify-center text-sm font-bold hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1 dark:focus:ring-offset-gray-800 shadow-md transition-colors"
             title="Remove image"
           >
             ×
           </button>
         </div>
       )}
    </div>
  );
};

export default PromptInputArea;
