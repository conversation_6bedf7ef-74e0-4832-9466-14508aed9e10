import React, { useEffect, useRef, useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { getSocket } from '../services/socketService';
import { BACKEND_BASE_URL } from '../config'; // Import the base URL
import { DiscussionRecord, GeneratedImageMessage } from '../types';

// Define props interface for the new structure
interface AgentDiscussionPanelProps {
  discussion: DiscussionRecord; // Object mapping agentName/key to accumulated content
  order: string[]; // Array of agentName/keys in the order they appeared
  // We might need setters if the parent needs to know about the image, but let's manage locally first
  // setDiscussion: React.Dispatch<React.SetStateAction<DiscussionRecord>>;
  // setOrder: React.Dispatch<React.SetStateAction<string[]>>;
}

const AgentDiscussionPanel: React.FC<AgentDiscussionPanelProps> = ({ discussion, order }) => {
  const discussionEndRef = useRef<HTMLDivElement>(null); // Ref for auto-scrolling
  // State to hold image generation results from the socket, separate from props
  const [generatedImages, setGeneratedImages] = useState<Record<string, GeneratedImageMessage>>({});
  // State to track image loading status (key: loading status)
  const [imageLoadStatus, setImageLoadStatus] = useState<Record<string, 'loading' | 'loaded' | 'error'>>({});

  // Combine discussion from props with images from local state for rendering
  const combinedDiscussion = { ...discussion, ...generatedImages };
  const imageKeys = Object.keys(generatedImages);
  const combinedOrder = [...order, ...imageKeys.filter(key => !order.includes(key))];

  const prevOrderLength = useRef(order.length);

  // Effect to clear local state when a new chat starts
  useEffect(() => {
    // A new chat is detected if the order array becomes much smaller or empty
    if (order.length === 0 && prevOrderLength.current > 0) {
      setGeneratedImages({});
      setImageLoadStatus({});
    }
    prevOrderLength.current = order.length;
  }, [order]);

  // Auto-scroll to bottom when discussion updates
  useEffect(() => {
    discussionEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [combinedDiscussion, combinedOrder]); // Trigger scroll on combined state change

  // --- Socket Listener for Image Generation ---
  useEffect(() => {
    const socket = getSocket();
    if (!socket) return;

    // --- UPDATE handleImageGenerated ---
    const handleImageGenerated = (data: {
        localImagePath?: string; // Expect local path
        imageUrl?: string;
        imageDataUrl?: string;
        error?: string;
        key: string; // Expect the key sent from backend
     }) => {
      console.log('Received image_generated event:', data);
      const messageData: GeneratedImageMessage = {
        type: 'image_generated',
        key: data.key || `image_gen_${Date.now()}`, // Use key from backend or generate fallback
        localImagePath: data.localImagePath || '', // Ensure it's not undefined
        imageUrl: data.imageUrl, // Keep for potential future use/reference
        imageDataUrl: data.imageDataUrl, // Keep for potential future use/reference
        error: data.error,
      };

      // Add the new image message to the local `generatedImages` state
      setGeneratedImages(prev => ({
        ...prev,
        [messageData.key]: messageData
      }));
    };

    socket.on('image_generated', handleImageGenerated);

    // Cleanup listener on component unmount
    return () => {
      socket.off('image_generated', handleImageGenerated);
    };
  }, []); // Run only once on mount
  // --- End Socket Listener ---

  // Helper to format agent name/key for display
  const formatSpeaker = (key: string): string => {
      if (key.startsWith('user_')) return 'User';
      if (key.startsWith('system_')) return 'System';
      if (key.startsWith('Error_')) return 'Error';
      if (key.startsWith('image_gen_')) return 'Image Generator'; // Handle image gen message
      // Extract agent name if possible (e.g., "Agent 1 (Google/gemini-2.0-flash)")
      const match = key.match(/^(Agent \d+.*)/);
      return match ? match[1] : key; // Fallback to the key itself
  };

  return (
    <div className="h-full bg-gray-100 dark:bg-gray-850 rounded-lg p-4 overflow-y-auto">
      {combinedOrder.length === 0 ? (
        <p className="text-gray-500 dark:text-gray-400 italic text-center text-lg mt-8">Agent discussion will appear here...</p>
      ) : (
        <div className="space-y-5 text-base"> {/* Increased spacing and font size */}
          {combinedOrder.map((key) => {
            const messageItem = combinedDiscussion[key];
            const speaker = formatSpeaker(key); // Format speaker name

            let contentElement: React.ReactNode;

            // --- UPDATE Rendering Logic ---
            if (typeof messageItem === 'string') {
              // Check if the message contains an image path reference
              const imagePathMatch = messageItem.match(/\(Viewable in chat\): (\/generated_images\/[\w\-\.]+)/i) ||
                                    messageItem.match(/Generated Image.*?: (\/generated_images\/[\w\-\.]+)/i);

              if (imagePathMatch && imagePathMatch[1]) {
                // This is a text message containing an image path reference
                const imagePath = imagePathMatch[1];
                const imageUrl = `${BACKEND_BASE_URL}${imagePath}`;
                const imageKey = `extracted_image_${imagePath}`; // Use stable key based on path

                contentElement = (
                  <div>
                    <ReactMarkdown className="prose prose-lg dark:prose-invert max-w-none mb-2" remarkPlugins={[remarkGfm]}>
                      {messageItem.replace(imagePathMatch[0], '')}
                    </ReactMarkdown>
                    <div className="relative group">
                      <img
                        src={imageUrl}
                        alt="Generated by AI"
                        className="max-w-xs max-h-64 my-2 rounded border border-gray-300 dark:border-gray-600"
                        onLoad={() => setImageLoadStatus(prev => ({ ...prev, [imageKey]: 'loaded' }))}
                        onError={() => setImageLoadStatus(prev => ({ ...prev, [imageKey]: 'error' }))}
                      />
                      <button
                        onClick={() => {
                          const link = document.createElement('a');
                          link.href = imageUrl;
                          link.download = imageUrl.split('/').pop() || 'generated-image.png';
                          document.body.appendChild(link);
                          link.click();
                          document.body.removeChild(link);
                        }}
                        className="absolute bottom-2 right-2 bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full shadow-md opacity-0 group-hover:opacity-100 transition-opacity"
                        title="Download Image"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                      </button>
                    </div>
                  </div>
                );
              } else {
                // Regular text message without image reference
                contentElement = (
                  <ReactMarkdown className="prose prose-lg dark:prose-invert max-w-none" remarkPlugins={[remarkGfm]}>
                    {messageItem || ''}
                  </ReactMarkdown>
                );
              }
            } else if (messageItem && messageItem.type === 'image_generated') {
              // Image generation result message
              if (messageItem.error) {
                contentElement = <p className="text-red-500 dark:text-red-400">Error generating image: {messageItem.error}</p>;
              } else if (messageItem.localImagePath) { // <<<--- PRIORITIZE localImagePath
                const imageUrl = `${BACKEND_BASE_URL}${messageItem.localImagePath}`; // Construct full URL
                const currentStatus = imageLoadStatus[messageItem.key] || 'loading';

                if (currentStatus === 'loading') {
                  contentElement = (
                    <div className="max-w-xs max-h-64 my-2 rounded border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-700 flex items-center justify-center aspect-square">
                      <p className="text-sm text-gray-500 dark:text-gray-400 italic p-2">Loading image...</p>
                      {/* Hidden image tag to trigger loading */}
                      <img
                        src={imageUrl}
                        alt="Loading generated AI image"
                        className="hidden" // Hide the actual image while loading
                        onLoad={() => setImageLoadStatus(prev => ({ ...prev, [messageItem.key]: 'loaded' }))}
                        onError={(e) => {
                          console.error("Failed to load saved image:", imageUrl, e);
                          setImageLoadStatus(prev => ({ ...prev, [messageItem.key]: 'error' }));
                        }}
                      />
                    </div>
                  );
                } else if (currentStatus === 'loaded') {
                  contentElement = (
                    <div className="relative group">
                      <img
                        src={imageUrl}
                        alt="Generated by AI"
                        className="max-w-xs max-h-64 my-2 rounded border border-gray-300 dark:border-gray-600"
                        // No need for onLoad/onError here as status is already 'loaded'
                      />
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          const link = document.createElement('a');
                          link.href = imageUrl;
                          link.download = imageUrl.split('/').pop() || 'generated-image.png';
                          document.body.appendChild(link);
                          link.click();
                          document.body.removeChild(link);
                        }}
                        className="absolute bottom-2 right-2 bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full shadow-md opacity-0 group-hover:opacity-100 transition-opacity"
                        title="Download Image"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                      </button>
                    </div>
                  );
                } else { // currentStatus === 'error'
                  contentElement = (
                     <div className="max-w-xs my-2 p-2 rounded border border-red-400 dark:border-red-600 bg-red-50 dark:bg-red-900/30 flex items-center justify-center">
                       <p className="text-sm text-red-600 dark:text-red-400">Failed to load image.</p>
                     </div>
                  );
                }
              } else {
                  // Fallback if localImagePath isn't present but no error
                  contentElement = <p className="text-gray-500 dark:text-gray-400 italic">(Image generated, but path missing)</p>;
              }
            } else {
               // Fallback for unexpected data structure
               contentElement = <p className="text-gray-500 dark:text-gray-400 italic">(Unknown message format)</p>;
            }
            // --- End Updated Rendering Logic ---

            return (
              <div key={key} className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                <p className="font-semibold text-primary dark:text-primary-light mb-2 text-lg">{speaker}:</p>
                {contentElement} {/* Render the determined content */}
              </div>
            );
          })}
          {/* Empty div at the end to scroll to */}
          <div ref={discussionEndRef} />
        </div>
      )}
    </div>
  );
};

export default AgentDiscussionPanel;
