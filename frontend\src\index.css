@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Additional global styles */
body {
  min-height: 100vh;
  margin: 0;
  font-size: 16px; /* Ensure base font size is 16px */
  line-height: 1.6; /* Improve line height for readability */
}

/* Improve default text sizes for better readability */
@layer base {
  html {
    font-size: 16px; /* Ensure 1rem = 16px */
  }

  /* Override Tailwind's default text sizes to be larger */
  .text-xs { font-size: 0.875rem; line-height: 1.5; } /* 14px instead of 12px */
  .text-sm { font-size: 1rem; line-height: 1.5; } /* 16px instead of 14px */
  .text-base { font-size: 1.125rem; line-height: 1.6; } /* 18px instead of 16px */
  .text-lg { font-size: 1.25rem; line-height: 1.6; } /* 20px instead of 18px */
  .text-xl { font-size: 1.375rem; line-height: 1.6; } /* 22px instead of 20px */
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-200 dark:bg-gray-700;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-400 dark:bg-gray-600 rounded;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500 dark:bg-gray-500;
}

/* Vertical writing for collapsed panels */
.writing-vertical {
  writing-mode: vertical-rl;
  text-orientation: mixed;
  white-space: nowrap;
}
