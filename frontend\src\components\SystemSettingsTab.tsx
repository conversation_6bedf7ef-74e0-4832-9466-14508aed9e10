import React from 'react';
import { useConfigStore } from '../store/configStore';

const SystemSettingsTab: React.FC = () => {
  // Select state and action from the Zustand store
  const {
    generalInstructions,
    setGeneralInstructions,
    baseInstructions,
    setBaseInstructions,
    useBaseInstructions,      // Destructure useBaseInstructions
    setUseBaseInstructions,   // Destructure setUseBaseInstructions
    maxAgentRuns,
    setMaxAgentRuns,
    maxContextWindow,
    setMaxContextWindow,
    workingContextSize,
    setWorkingContextSize
  } = useConfigStore((state) => ({
    generalInstructions: state.generalInstructions,
    setGeneralInstructions: state.setGeneralInstructions,
    baseInstructions: state.baseInstructions,
    setBaseInstructions: state.setBaseInstructions,
    useBaseInstructions: state.useBaseInstructions,
    setUseBaseInstructions: state.setUseBaseInstructions,
    maxAgentRuns: state.maxAgentRuns,
    setMaxAgentRuns: state.setMaxAgentRuns,
    maxContextWindow: state.maxContextWindow,
    setMaxContextWindow: state.setMaxContextWindow,
    workingContextSize: state.workingContextSize,
    setWorkingContextSize: state.setWorkingContextSize,
  }));

  return (
    <div className="space-y-6 p-1">
      <h3 className="text-xl font-semibold mb-2 text-gray-800 dark:text-gray-200">System Settings</h3>

      <div>
        <label htmlFor="generalInstructions" className="block text-base font-medium text-gray-700 dark:text-gray-300 mb-1">
          General Instructions / System Prompt
        </label>
        <textarea
          id="generalInstructions"
          rows={10} // Adjust rows as needed
          value={generalInstructions}
          onChange={(e) => setGeneralInstructions(e.target.value)}
          placeholder="Enter general instructions or a system prompt that applies to the overall task or all agents..."
          className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-base bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
        />
        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
          This prompt provides overall guidance for the agent collaboration process. If left empty, the Default Base Instructions below will be used.
        </p>
      </div>

       {/* Base Instructions Section */}
       <div className="space-y-2">
         <div className="flex items-center">
           <input
             id="useBaseInstructions"
             name="useBaseInstructions"
             type="checkbox"
             checked={useBaseInstructions}
             onChange={(e) => setUseBaseInstructions(e.target.checked)}
             className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:focus:ring-offset-gray-800"
           />
           <label htmlFor="useBaseInstructions" className="ml-2 block text-base font-medium text-gray-700 dark:text-gray-300">
             Use Default Base Instructions (if General Instructions is empty)
           </label>
         </div>
         <textarea
          id="baseInstructions"
          rows={6} // Adjust rows as needed
           value={baseInstructions}
           onChange={(e) => setBaseInstructions(e.target.value)}
           placeholder="Enter default base instructions for agents..."
           className={`mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-base bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 ${!useBaseInstructions ? 'opacity-50 cursor-not-allowed' : ''}`}
           disabled={!useBaseInstructions} // Disable textarea if checkbox is unchecked
         />
       </div>
       {/* End Base Instructions Section */}

      {/* Max Agent Runs Input */}
      <div>
        <label htmlFor="maxAgentRuns" className="block text-base font-medium text-gray-700 dark:text-gray-300 mb-1">
          Max Runs Per Agent
        </label>
        <input
          type="number"
          id="maxAgentRuns"
          min="1"
          max="3" // Setting a reasonable upper limit for now
          value={maxAgentRuns}
          onChange={(e) => setMaxAgentRuns(parseInt(e.target.value, 10))}
          className="mt-1 block w-24 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-base bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
        />
        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
          Maximum number of times each agent will run in sequence (e.g., 1 for a single pass, 2 for a refinement pass).
        </p>
      </div>
      {/* End Max Agent Runs Input */}

      {/* Token Limit Settings */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
        <h4 className="text-lg font-semibold mb-3 text-gray-700 dark:text-gray-300">Token Limit Settings</h4>

        {/* Max Context Window */}
        <div className="mb-4">
          <label htmlFor="maxContextWindow" className="block text-base font-medium text-gray-700 dark:text-gray-300 mb-1">
            Max Context Window Size
          </label>
          <input
            type="number"
            id="maxContextWindow"
            min="1000"
            max="100000"
            step="1000"
            value={maxContextWindow}
            onChange={(e) => setMaxContextWindow(parseInt(e.target.value, 10))}
            className="mt-1 block w-36 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-base bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          />
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            Maximum allowed context window size in tokens (default: 20000). This is the absolute maximum size for the conversation history.
          </p>
        </div>

        {/* Working Context Size */}
        <div>
          <label htmlFor="workingContextSize" className="block text-base font-medium text-gray-700 dark:text-gray-300 mb-1">
            Working Context Size
          </label>
          <input
            type="number"
            id="workingContextSize"
            min="1000"
            max={maxContextWindow}
            step="1000"
            value={workingContextSize}
            onChange={(e) => setWorkingContextSize(parseInt(e.target.value, 10))}
            className="mt-1 block w-36 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-base bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          />
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            Target context window size in tokens (default: 16384). This is the target size for the conversation history sent to the LLM.
          </p>
        </div>
      </div>
      {/* End Token Limit Settings */}

    </div>
  );
};

export default SystemSettingsTab;
