import dotenv from 'dotenv';
// Load environment variables FIRST
dotenv.config();

import express from 'express';
import http from 'http';
import { Server as SocketIOServer } from 'socket.io';
import cors from 'cors';
import mongoose from 'mongoose'; // Import Mongoose
import path from 'path'; // Import path
import fs from 'fs'; // Import fs for directory operations
import passport from './config/passport'; // Import configured passport instance

// Import security middleware
import {
  helmetConfig,
  generalRateLimit,
  authRateLimit,
  llmRateLimit,
  uploadRateLimit,
  mongoSanitizeConfig,
  hppConfig,
  additionalSecurity,
  validateInput
} from './middleware/securityMiddleware';

// Import routes
import configRoutes from './routes/configRoutes';
import ragRoutes from './routes/ragRoutes';
import conversationRoutes from './routes/conversationRoutes'; // Import Conversation routes
import llmRoutes from './routes/llmRoutes'; // Import LLM routes
import authRoutes from './routes/authRoutes'; // Import Auth routes
import apiKeyRoutes from './routes/apiKeyRoutes'; // Import API Key routes
import importExportRoutes from './routes/importExportRoutes'; // Import Import/Export routes
// Import middleware
import { socketProtect } from './middleware/authMiddleware'; // Import Socket.IO auth middleware
// Import services
import { performInternetSearch } from './services/internetSearchService';
import { runAgentOrchestration, AgentOrchestrationOptions } from './services/agentOrchestrator'; // Import the orchestrator and options type

// Map to store cancellation flags for ongoing orchestrations
const cancellationFlags = new Map<string, boolean>();

// Define expected structure for incoming config data (mirroring frontend store state)
export interface IncomingAgentConfig { // Added export
  provider: string;
  model: string;
  instructions: string;
  useDefaultInstructions?: boolean;
  internetEnabled?: boolean;
  // LLM Parameters
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  presencePenalty?: number;
  frequencyPenalty?: number;
}
interface IncomingInternetSettings {
  enabled: boolean;
  searchProvider: string;
  searchApiKey: string;
  googleCxId?: string; // Google CX ID
  includedDomains: string[];
  excludedDomains: string[];
}

interface SearchAgentSettings {
  provider: string;
  model: string;
  temperature: number;
  maxTokens?: number;
  systemPrompt?: string;
}
interface IncomingRagSettings {
  enabled: boolean;
  chunkingStrategy: string;
  chunkSize: number;
  chunkOverlap: number;
  embeddingModel: string;
  retrievalNResults: number;
  retrievalThreshold: number;
  useReranking: boolean;
  useQueryExpansion: boolean;
}
// Export the interface so it can be imported elsewhere
export interface IncomingConfigData {
  agentCount: number;
  generalInstructions: string;
  agentConfigurations: IncomingAgentConfig[];
  internetSettings: IncomingInternetSettings;
  searchAgentSettings?: SearchAgentSettings; // Add search agent settings
  ragSettings: IncomingRagSettings;
  maxAgentRuns?: number;
  baseInstructions?: string;
  useBaseInstructions?: boolean; // Add useBaseInstructions flag
  // Token limit settings
  maxContextWindow?: number;
  workingContextSize?: number;
}

// --- Database Connection ---
const connectDB = async () => {
  try {
    const mongoUri = process.env.MONGO_URI;
    if (!mongoUri) {
      console.warn('WARNING: MONGO_URI is not defined in .env file. Running without database (limited functionality).');
      return false;
    }
    await mongoose.connect(mongoUri);
    console.log('MongoDB Connected...');
    return true;
  } catch (err) {
    if (err instanceof Error) {
        console.warn('MongoDB Connection Warning:', err.message);
        console.warn('Running without database (limited functionality). You can test the frontend.');
    } else {
        console.warn('Unknown MongoDB Connection Warning:', err);
    }
    return false;
  }
};

// Try to connect to database, but don't exit if it fails
connectDB().then(connected => {
  if (!connected) {
    console.log('🚀 Server running in DEMO MODE (no database)');
    console.log('📝 To enable full functionality, set up MongoDB and update MONGO_URI in .env');
  }
});
// --- End Database Connection ---


// Initialize Express app
const app = express();
const server = http.createServer(app);

// Configure Socket.IO with CORS
const io = new SocketIOServer(server, {
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:5173',
    methods: ['GET', 'POST'],
    credentials: true,
  },
});

// Security Middleware (apply early)
app.use(helmetConfig);
app.use(additionalSecurity);
app.use(validateInput);
app.use(mongoSanitizeConfig);
app.use(hppConfig);

// General rate limiting
app.use(generalRateLimit);

// CORS Middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:5173',
  credentials: true,
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Initialize Passport
app.use(passport.initialize());
// Note: We don't need passport.session() because we are using JWTs for session management after login,
// but passport.initialize() is required for strategies to work.

// --- Define Public Directory Path ---
// Resolve path relative to the *project root* or a known location
// This assumes 'public' is in the 'backend' directory, adjust if it's at the project root
const PUBLIC_DIR = path.resolve(__dirname, '../public'); // Goes up one level from dist/src to backend/public

// --- Ensure Knowledge Bases Directory Exists ---
const KNOWLEDGE_BASE_ROOT = path.resolve(__dirname, '../../knowledge_bases');
try {
  if (!fs.existsSync(KNOWLEDGE_BASE_ROOT)) {
    console.log(`Creating knowledge_bases directory at: ${KNOWLEDGE_BASE_ROOT}`);
    fs.mkdirSync(KNOWLEDGE_BASE_ROOT, { recursive: true });

    // Also create default_kb subdirectory
    const DEFAULT_KB_PATH = path.join(KNOWLEDGE_BASE_ROOT, 'default_kb');
    if (!fs.existsSync(DEFAULT_KB_PATH)) {
      console.log(`Creating default_kb directory at: ${DEFAULT_KB_PATH}`);
      fs.mkdirSync(DEFAULT_KB_PATH, { recursive: true });
    }
  } else {
    console.log(`Knowledge bases directory exists at: ${KNOWLEDGE_BASE_ROOT}`);
  }
} catch (error) {
  console.error(`Error creating knowledge_bases directory: ${error}`);
}

// --- Add Static File Serving ---
// Serve images from backend/public/generated_images at the URL /generated_images
app.use('/generated_images', express.static(path.join(PUBLIC_DIR, 'generated_images')));
console.log(`Serving static files from: ${path.join(PUBLIC_DIR, 'generated_images')}`);


// API Routes
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Mount Configuration Routes
app.use('/api/config', configRoutes);

// Mount RAG Routes with upload rate limiting
app.use('/api/rag', uploadRateLimit, ragRoutes);

// Mount Conversation Routes
app.use('/api/conversations', conversationRoutes);

// Mount LLM Routes with LLM-specific rate limiting
app.use('/api/llm', llmRateLimit, llmRoutes);

// Mount Auth Routes with strict rate limiting
app.use('/api/auth', authRateLimit, authRoutes);

// Mount API Key Routes
app.use('/api/apikeys', apiKeyRoutes);

// Mount Import/Export Routes
app.use('/api/import-export', importExportRoutes);

// --- Socket.IO Middleware ---
io.use(socketProtect); // Apply authentication middleware to all incoming socket connections

// --- Other API Routes (Placeholders) ---

// All API endpoints are now handled by their respective route files

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log(`Client connected: ${socket.id}`);

  // Assign the client to a room based on socket ID
  const room = `client-${socket.id}`;
  socket.join(room);

  // Handle disconnect
  socket.on('disconnect', () => {
    console.log(`Client disconnected: ${socket.id}`);
    // Clean up cancellation flag on disconnect
    cancellationFlags.delete(socket.id);
    socket.leave(room);
  });

  // Add test ping/pong functionality
  socket.on('ping', (data) => {
    console.log(`Received ping from ${socket.id}:`, data);
    socket.emit('pong', { reply: 'Hello from server', timestamp: new Date().toISOString() });
  });

  // --- Modify 'image_generated' handler if it exists ---
  // This event is now EMITTED BY THE ORCHESTRATOR, not handled here.
  // Remove any socket.on('image_generated', ...) handler here if present.

  // --- Modify 'send_prompt' handler ---
  // Ensure it passes imageData to runAgentOrchestration
  socket.on('send_prompt', async (data: {
      prompt: string;
      config: IncomingConfigData;
      conversationId?: string;
      imageDataUrl?: string | null; // Image data URL if available
      isImageRequest?: boolean; // Explicit flag for image generation
  }) => {
    console.log(`Received prompt from ${socket.id}:`, data.prompt.substring(0, 100) + (data.prompt.length > 100 ? '...' : ''));
    // console.log(`Received config from ${socket.id}:`, data.config); // Can be verbose
    console.log(`Received conversationId from ${socket.id}:`, data.conversationId);
    console.log(`Received imageDataUrl from ${socket.id}:`, data.imageDataUrl ? `Data URL (length: ${data.imageDataUrl.length})` : 'None');
    console.log(`Received isImageRequest from ${socket.id}:`, data.isImageRequest ? 'Yes' : 'No');
    console.log(`Received searchAgentSettings from ${socket.id}:`, data.config.searchAgentSettings ? 'Present' : 'Missing');

    // Log more details about the image data if present
    if (data.imageDataUrl) {
      console.log(`[DEBUG] Image data received. First 100 chars: ${data.imageDataUrl.substring(0, 100)}...`);
      // Check if it's a valid data URL format
      if (data.imageDataUrl.startsWith('data:image/')) {
        console.log(`[DEBUG] Valid image data URL format detected.`);
      } else {
        console.warn(`[DEBUG] WARNING: Image data URL does not have expected format!`);
      }
    }
    if (data.config.searchAgentSettings) {
      console.log(`SearchAgentSettings details:`, {
        provider: data.config.searchAgentSettings.provider,
        model: data.config.searchAgentSettings.model,
        temperature: data.config.searchAgentSettings.temperature,
        maxTokens: data.config.searchAgentSettings.maxTokens
      });
    }

    // Clear any previous cancellation flag for this socket before starting
    cancellationFlags.delete(socket.id);

    // Emit processing started event back to the specific client
    socket.emit('processing_started', { conversationId: data.conversationId });

    // --- Run Agent Orchestration ---
    // Orchestrator now handles internet search, RAG, agent turns, image generation,
    // emitting updates, and saving history.
    try {
        // Define cancellation check function for this specific run
        const isCancelled = () => cancellationFlags.get(socket.id) === true;

        const options: AgentOrchestrationOptions = {
            socket,
            conversationId: data.conversationId,
            isCancelled, // Pass the cancellation check function
            isExplicitImageRequest: data.isImageRequest || false, // Pass the explicit image request flag
        };

        // Pass imageData to the orchestrator
        // Orchestrator returns final *text* answer or empty string for image gen/cancellation
        const finalAnswer = await runAgentOrchestration(
            data.prompt,
            data.config,
            options, // Pass the options object
            data.imageDataUrl // Pass image data URL
        );

        // Check if cancelled *after* the run potentially finished early
        if (isCancelled()) {
             console.log(`Orchestration cancelled for ${socket.id} during execution.`);
             // Ensure discussion_completed is emitted even if cancelled
             socket.emit('discussion_completed');
        } else {
            // Emit the final answer (if any) and completion event only if not cancelled
            // Only emit final answer if it's not an image generation flow (orchestrator returns "" for image gen)
            if (finalAnswer) {
                 socket.emit('update_final_answer', finalAnswer);
            }
            socket.emit('discussion_completed');
            console.log(`Orchestration completed normally for ${socket.id}.`);
        }

    } catch (orchestrationError: any) {
        // Orchestrator should ideally handle its own errors and emit 'processing_error'
        // This catch block is for unexpected top-level errors in the handler itself
        console.error("Error in top-level send_prompt handler:", orchestrationError);
        // Ensure completion is signaled even on unexpected top-level error
        if (!cancellationFlags.get(socket.id)) { // Avoid duplicate emit if cancelled
            socket.emit('processing_error', { message: `Orchestration failed: ${orchestrationError.message}` });
            socket.emit('discussion_completed');
        }
    } finally {
         // Always clean up the cancellation flag for this socket ID
         cancellationFlags.delete(socket.id);
    }
    // --- End Agent Orchestration ---
  });

  // Handle 'stop_processing' event
  socket.on('stop_processing', () => {
      console.log(`Received stop_processing event from ${socket.id}`);
      cancellationFlags.set(socket.id, true); // Set the flag to true
  });
});

// Start server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`API available at http://localhost:${PORT}/api`);
  console.log(`Socket.IO available at http://localhost:${PORT}`);
});

export { app, server, io };
